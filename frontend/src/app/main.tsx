import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { routeTree } from './routeTree.gen';
import { createRouter, RouterProvider } from '@tanstack/react-router';
import * as Sentry from '@sentry/react';
import './globals.css';

const router = createRouter({
    routeTree,
    defaultOnCatch(error, errorInfo) {
        Sentry.captureException(error);
    },
});

Sentry.init({
    dsn: import.meta.env.VITE_SENTRY_DSN,
    enabled: import.meta.env.VITE_SENTRY_ENABLED,

    integrations: [Sentry.tanstackRouterBrowserTracingIntegration(router)],

    tracesSampleRate: 0.5,
});

declare module '@tanstack/react-router' {
    interface Register {
        router: typeof router;
    }
}

const rootElement = document.querySelector('#root')!;
if (!rootElement.innerHTML) {
    // const root = createRoot(rootElement, {
    //     onUncaughtError: Sentry.reactErrorHandler((error, errorInfo) => {
    //         console.warn('Uncaught error', error, errorInfo.componentStack);
    //     }),
    //     // Callback called when React catches an error in an ErrorBoundary.
    //     onCaughtError: Sentry.reactErrorHandler(),
    //     // Callback called when React automatically recovers from errors.
    //     onRecoverableError: Sentry.reactErrorHandler(),
    // });
    const root = createRoot(rootElement);
    root.render(
        <StrictMode>
            <RouterProvider router={router} />
        </StrictMode>,
    );
}
