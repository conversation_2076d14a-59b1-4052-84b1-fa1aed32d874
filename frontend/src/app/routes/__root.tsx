import { createRootRoute, Outlet, useRouter } from '@tanstack/react-router';
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools';

export const Route = createRootRoute({
    component: () => (
        <>
            <h1>Sidebar</h1>
            <Outlet />
            <TanStackRouterDevtools />
        </>
    ),
    errorComponent: () => <div>error</div>,
});

import * as Sentry from '@sentry/react';
import { useEffect } from 'react';
export function YourCustomRootErrorBoundary() {
    const { state } = useRouter();
    useEffect(() => {
        Sentry.captureException(state);
    }, [state]);
    return (
        <div>
            <h1>Ouch!</h1>
        </div>
    );
}
