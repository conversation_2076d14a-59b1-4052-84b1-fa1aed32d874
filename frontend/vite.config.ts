import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tanstackRouter from '@tanstack/router-plugin/vite';
import tailwindcss from '@tailwindcss/vite';

// https://vite.dev/config/
export default defineConfig({
    plugins: [
        tanstackRouter({
            target: 'react',
            autoCodeSplitting: true,
            routesDirectory: './src/app/routes',
            generatedRouteTree: './src/app/routeTree.gen.ts',
        }),
        tailwindcss(),
        react(),
    ],

    server: {
        port: 3005,
    },

    resolve: {
        alias: {
            '@': '/src',
        },
    },
});
